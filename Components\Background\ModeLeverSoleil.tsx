import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';

interface ModeLeverSoleilProps {
  isActive: boolean;
  intensity?: number; // 0-1, progression du lever de soleil
  autoStart?: boolean; // Démarrage automatique de la séquence
}

/**
 * 🌅 MODULE LEVER DE SOLEIL - TOUT EN UN
 *
 * CISCO INSTRUCTIONS: Ce module contient TOUT pour le lever du soleil
 * ✅ DÉMARRAGE EN MODE NUIT (tout sombre, étoiles visibles)
 * ✅ SONS NUIT → LEVER DE SOLEIL (temporisation)
 * ✅ Éclairage global spécifique au lever
 * ✅ Position du soleil qui se lève
 * ✅ Quelques étoiles qui disparaissent progressivement
 * ✅ Éclairage du paysage qui s'éclaircit
 * ✅ Dégradé de couleurs aube (monte du bas vers le haut)
 * ✅ TEMPORISATION AUTOMATIQUE progressive
 */
const ModeLeverSoleil: React.FC<ModeLeverSoleilProps> = ({
  isActive,
  intensity = 0.0, // 🔧 CISCO: Démarrage à 0 (nuit complète)
  autoStart = true
}) => {
  // 🎯 RÉFÉRENCES POUR LES ÉLÉMENTS
  const containerRef = useRef<HTMLDivElement>(null);
  const sunRef = useRef<HTMLDivElement>(null);
  const starsContainerRef = useRef<HTMLDivElement>(null);
  const globalLightRef = useRef<HTMLDivElement>(null);
  const landscapeRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // 🌅 ÉTAT INTERNE POUR LA PROGRESSION AUTOMATIQUE
  const [currentIntensity, setCurrentIntensity] = useState(0.0); // Démarrage nuit complète
  const [isProgressing, setIsProgressing] = useState(false);
  const [currentPhase, setCurrentPhase] = useState<'nuit' | 'transition' | 'lever'>('nuit');

  // 🌅 PALETTES COULEURS LEVER DE SOLEIL (selon Cisco.md)
  const SUNRISE_COLORS = {
    // Couleurs chaudes et douces, allant du bleu nuit au rose, orange et jaune clair
    deepNight: '#1F214D',    // Bleu nuit profond
    imperial: '#50366F',     // Violet impérial
    roseDark: '#BF3475',     // Rose foncé
    orangeChinese: '#EE6C45', // Orange chinois
    yellowLight: '#FFCE61',   // Jaune clair
    yellowVeryLight: '#FFE58A' // Jaune très clair
  };

  // 🌙 COULEURS NUIT PROFONDE (démarrage)
  const NIGHT_COLORS = {
    nightSky: '#041A40',     // Bleu Nuit / Night Sky
    darkBlue: '#00008B',     // Bleu Foncé / Dark Blue
    midnightBlue: '#00316E', // Bleu Nuit Profond
    oxfordBlue: '#00224B',   // Bleu Oxford
    almostBlack: '#0B1426'   // Presque Noir
  };

  // 🎵 GESTION AUDIO SELON CISCO INSTRUCTIONS
  const AUDIO_CONFIG = {
    night: '/sounds/nuit-profonde/night-atmosphere-with-crickets-374652.mp3',
    sunrise: '/sounds/lever-soleil/Lever_soleil-nature.mp3',
    transitionDelay: 30000, // 30 secondes avant transition audio
    fadeOutDuration: 5000,  // 5 secondes de fade out
    fadeInDuration: 3000    // 3 secondes de fade in
  };

  // 🌟 GÉNÉRATION DES ÉTOILES POUR LE LEVER (CISCO: scintillement naturel)
  const generateSunriseStars = () => {
    const stars = [];
    // CISCO: Plus d'étoiles au démarrage (mode nuit), disparaissent progressivement
    const starCount = 25; // Plus d'étoiles pour le démarrage nuit

    for (let i = 0; i < starCount; i++) {
      const star = {
        id: i,
        x: Math.random() * 100, // Position en %
        y: Math.random() * 70,  // Plus de zone pour les étoiles (70% au lieu de 60%)
        size: Math.random() * 3 + 1.5, // 1.5-4.5px (plus visibles)
        opacity: Math.random() * 0.6 + 0.4, // 0.4-1.0 (plus visibles au démarrage)
        // CISCO: CORRECTION - Délais très aléatoires pour éviter effet guirlande
        twinkleDelay: Math.random() * 15 + Math.random() * 10, // 0-25s très aléatoire
        twinkleDuration: 2 + Math.random() * 6, // 2-8s durée variable
        type: Math.random() > 0.7 ? 'big' : 'normal' // 30% de grosses étoiles
      };
      stars.push(star);
    }
    return stars;
  };

  // 🎵 GESTION AUDIO PROGRESSIVE
  const playNightSound = () => {
    if (audioRef.current) {
      audioRef.current.pause();
    }

    audioRef.current = new Audio(AUDIO_CONFIG.night);
    audioRef.current.loop = true;
    audioRef.current.volume = 0.6;
    audioRef.current.play().catch(console.warn);
    console.log('🌙 Son de nuit démarré');
  };

  const transitionToSunriseSound = () => {
    if (!audioRef.current) return;

    // Fade out du son de nuit
    const fadeOutInterval = setInterval(() => {
      if (audioRef.current && audioRef.current.volume > 0.1) {
        audioRef.current.volume = Math.max(0, audioRef.current.volume - 0.1);
      } else {
        clearInterval(fadeOutInterval);
        if (audioRef.current) {
          audioRef.current.pause();
        }

        // Démarrer le son du lever de soleil
        audioRef.current = new Audio(AUDIO_CONFIG.sunrise);
        audioRef.current.loop = true;
        audioRef.current.volume = 0;
        audioRef.current.play().catch(console.warn);

        // Fade in du son du lever
        const fadeInInterval = setInterval(() => {
          if (audioRef.current && audioRef.current.volume < 0.5) {
            audioRef.current.volume = Math.min(0.5, audioRef.current.volume + 0.05);
          } else {
            clearInterval(fadeInInterval);
          }
        }, 100);

        console.log('🌅 Transition vers son du lever de soleil');
      }
    }, 100);
  };

  // 🌅 ANIMATION DU SOLEIL QUI SE LÈVE (CISCO: derrière le paysage)
  const animateSunrise = (progressIntensity: number) => {
    if (!sunRef.current) return;

    // CISCO: Position du soleil - commence SOUS l'horizon, monte progressivement
    const sunStartY = 150; // Bien sous l'horizon (150% du container)
    const sunEndY = 25;    // Position haute pour le lever (25% du container)
    const currentSunY = sunStartY - (progressIntensity * (sunStartY - sunEndY));

    // Taille et intensité du soleil selon progression
    const sunScale = 0.3 + (progressIntensity * 0.7); // 0.3 à 1.0 (plus de variation)
    const sunOpacity = progressIntensity < 0.1 ? 0 : Math.min((progressIntensity - 0.1) * 1.2, 1.0);

    // CISCO: Le soleil ne devient visible qu'au-dessus de l'horizon du paysage
    const isAboveHorizon = currentSunY < 100; // 100% = horizon du paysage

    gsap.to(sunRef.current, {
      y: `${currentSunY}%`,
      scale: sunScale,
      opacity: isAboveHorizon ? sunOpacity : 0,
      duration: 3.0, // Plus lent pour effet naturel
      ease: "power1.inOut"
    });
  };

  // ⭐ ANIMATION DES ÉTOILES QUI DISPARAISSENT (CISCO: progressif naturel)
  const animateStarsFading = (progressIntensity: number) => {
    if (!starsContainerRef.current) return;

    // CISCO: Les étoiles disparaissent progressivement - plus naturel
    let starsOpacity;
    if (progressIntensity < 0.2) {
      starsOpacity = 1.0; // Pleine visibilité au début
    } else if (progressIntensity < 0.6) {
      starsOpacity = 1.0 - ((progressIntensity - 0.2) * 1.5); // Disparition progressive
    } else {
      starsOpacity = 0; // Complètement invisibles
    }

    gsap.to(starsContainerRef.current, {
      opacity: Math.max(starsOpacity, 0),
      duration: 4.0, // Plus lent pour effet naturel
      ease: "power2.out"
    });
  };

  // 💡 ÉCLAIRAGE GLOBAL PROGRESSIF (CISCO: synchronisé avec le soleil)
  const animateGlobalLighting = (progressIntensity: number) => {
    if (!globalLightRef.current) return;

    // CISCO: L'éclairage global suit la position du soleil
    let lightIntensity;
    if (progressIntensity < 0.1) {
      lightIntensity = 0; // Nuit complète
    } else if (progressIntensity < 0.5) {
      lightIntensity = (progressIntensity - 0.1) * 0.5; // Montée progressive
    } else {
      lightIntensity = 0.2 + (progressIntensity - 0.5) * 0.6; // Intensification
    }

    gsap.to(globalLightRef.current, {
      opacity: lightIntensity,
      duration: 3.0,
      ease: "power1.inOut"
    });
  };

  // 🏔️ ÉCLAIRAGE DU PAYSAGE (CISCO: s'éclaircit progressivement)
  const animateLandscapeLighting = (progressIntensity: number) => {
    if (!landscapeRef.current) return;

    // CISCO: Le paysage s'éclaircit avec le lever du soleil
    let brightness;
    if (progressIntensity < 0.1) {
      brightness = 0.15; // Très sombre au début (nuit)
    } else if (progressIntensity < 0.7) {
      brightness = 0.15 + (progressIntensity - 0.1) * 1.0; // Éclaircissement progressif
    } else {
      brightness = 0.75 + (progressIntensity - 0.7) * 0.5; // Pleine lumière
    }

    gsap.to(landscapeRef.current, {
      filter: `brightness(${brightness})`,
      duration: 3.5,
      ease: "power1.inOut"
    });
  };

  // 🎨 DÉGRADÉ DYNAMIQUE LEVER DE SOLEIL (CISCO: COULEURS changent, pas de rectangle)
  const createSunriseGradient = (progressIntensity: number) => {
    if (!containerRef.current) return;

    // CISCO: DÉGRADÉ FIXE - Seules les COULEURS changent avec la temporisation
    let gradient;

    if (progressIntensity < 0.1) {
      // CISCO: Démarrage - TOUT SOMBRE (bleu presque noir partout)
      gradient = `linear-gradient(to top,
        ${NIGHT_COLORS.almostBlack} 0%,
        ${NIGHT_COLORS.nightSky} 50%,
        ${NIGHT_COLORS.almostBlack} 100%)`;
    } else if (progressIntensity < 0.3) {
      // CISCO: Début - Couleurs nuit s'éclaircissent légèrement
      gradient = `linear-gradient(to top,
        ${NIGHT_COLORS.nightSky} 0%,
        ${NIGHT_COLORS.midnightBlue} 30%,
        ${NIGHT_COLORS.darkBlue} 70%,
        ${NIGHT_COLORS.nightSky} 100%)`;
    } else if (progressIntensity < 0.5) {
      // CISCO: Milieu - Transition vers couleurs aube
      gradient = `linear-gradient(to top,
        ${SUNRISE_COLORS.deepNight} 0%,
        ${SUNRISE_COLORS.imperial} 25%,
        ${NIGHT_COLORS.darkBlue} 75%,
        ${NIGHT_COLORS.nightSky} 100%)`;
    } else if (progressIntensity < 0.7) {
      // CISCO: Avancé - Couleurs chaudes du lever
      gradient = `linear-gradient(to top,
        ${SUNRISE_COLORS.roseDark} 0%,
        ${SUNRISE_COLORS.orangeChinese} 20%,
        ${SUNRISE_COLORS.imperial} 60%,
        ${SUNRISE_COLORS.deepNight} 100%)`;
    } else {
      // CISCO: Fin - Couleurs claires du jour
      gradient = `linear-gradient(to top,
        ${SUNRISE_COLORS.yellowLight} 0%,
        ${SUNRISE_COLORS.orangeChinese} 30%,
        ${SUNRISE_COLORS.roseDark} 60%,
        ${SUNRISE_COLORS.imperial} 100%)`;
    }

    gsap.to(containerRef.current, {
      backgroundImage: gradient,
      duration: 3.0, // Synchronisé avec temporisation
      ease: "power1.inOut"
    });
  };

  // 🎬 ORCHESTRATION COMPLÈTE DU LEVER DE SOLEIL (CISCO: tout synchronisé)
  const orchestrateSunrise = (progressIntensity: number) => {
    console.log(`🌅 CISCO Orchestration lever de soleil - Intensité: ${progressIntensity.toFixed(2)} - Phase: ${currentPhase}`);

    // Tuer l'animation précédente si elle existe
    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    // Créer une nouvelle timeline synchronisée
    timelineRef.current = gsap.timeline();

    // CISCO: Lancer toutes les animations en parallèle - TOUT synchronisé
    createSunriseGradient(progressIntensity);
    animateSunrise(progressIntensity);
    animateStarsFading(progressIntensity);
    animateGlobalLighting(progressIntensity);
    animateLandscapeLighting(progressIntensity);

    // CISCO: Gestion des phases et transitions audio
    if (progressIntensity < 0.1 && currentPhase !== 'nuit') {
      setCurrentPhase('nuit');
    } else if (progressIntensity >= 0.1 && progressIntensity < 0.6 && currentPhase !== 'transition') {
      setCurrentPhase('transition');
      // Transition audio après 30 secondes
      setTimeout(() => {
        if (currentPhase === 'transition') {
          transitionToSunriseSound();
        }
      }, AUDIO_CONFIG.transitionDelay);
    } else if (progressIntensity >= 0.6 && currentPhase !== 'lever') {
      setCurrentPhase('lever');
    }
  };

  // 🕐 TEMPORISATION AUTOMATIQUE (CISCO: progression automatique)
  const startAutomaticProgression = () => {
    if (isProgressing) return;

    console.log('🌅 CISCO: Démarrage progression automatique du lever de soleil');
    setIsProgressing(true);
    setCurrentIntensity(0.0); // Démarrage nuit complète

    // Démarrer le son de nuit
    playNightSound();

    // Progression automatique sur 8 minutes (480 secondes)
    const totalDuration = 480000; // 8 minutes en millisecondes
    const updateInterval = 1000; // Mise à jour chaque seconde
    const incrementPerSecond = 1.0 / (totalDuration / updateInterval);

    const progressionInterval = setInterval(() => {
      setCurrentIntensity(prev => {
        const newIntensity = prev + incrementPerSecond;

        if (newIntensity >= 1.0) {
          clearInterval(progressionInterval);
          setIsProgressing(false);
          console.log('🌅 CISCO: Lever de soleil terminé');
          return 1.0;
        }

        return newIntensity;
      });
    }, updateInterval);

    // Nettoyer l'intervalle si le composant se démonte
    return () => clearInterval(progressionInterval);
  };

  // 🔄 EFFET PRINCIPAL - RÉACTION AUX CHANGEMENTS (CISCO: démarrage automatique)
  useEffect(() => {
    if (isActive) {
      console.log('🌅 CISCO: ACTIVATION MODULE LEVER DE SOLEIL - Démarrage nuit complète');

      // CISCO: Démarrage automatique si demandé
      if (autoStart && !isProgressing) {
        startAutomaticProgression();
      } else {
        // Utiliser l'intensité fournie
        orchestrateSunrise(intensity);
      }
    } else {
      console.log('🌅 CISCO: DÉSACTIVATION MODULE LEVER DE SOLEIL');

      // Arrêter la progression automatique
      setIsProgressing(false);

      // Arrêter l'audio
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }

      // Fade out en douceur
      if (containerRef.current) {
        gsap.to(containerRef.current, {
          opacity: 0,
          duration: 2.0,
          ease: "power2.out"
        });
      }
    }

    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
      if (audioRef.current) {
        audioRef.current.pause();
      }
    };
  }, [isActive, intensity, autoStart]);

  // 🔄 EFFET POUR LA PROGRESSION AUTOMATIQUE
  useEffect(() => {
    if (isProgressing && isActive) {
      orchestrateSunrise(currentIntensity);
    }
  }, [currentIntensity, isProgressing, isActive]);

  // 🎨 GÉNÉRATION DES ÉTOILES AU MONTAGE
  const sunriseStars = generateSunriseStars();

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 pointer-events-none"
      style={{
        zIndex: 5, // CISCO: Au-dessus du background de base, sous le paysage
        opacity: isActive ? 1 : 0,
        transition: 'opacity 2s ease-in-out'
      }}
    >
      {/* 🌅 SOLEIL LEVANT (CISCO: derrière le paysage) */}
      <div
        ref={sunRef}
        className="absolute"
        style={{
          left: '75%', // Position à l'est pour le lever
          top: '50%',
          transform: 'translate(-50%, -50%)',
          width: '140px', // Légèrement plus grand
          height: '140px',
          borderRadius: '50%',
          background: `radial-gradient(circle,
            ${SUNRISE_COLORS.yellowVeryLight} 0%,
            ${SUNRISE_COLORS.yellowLight} 25%,
            ${SUNRISE_COLORS.orangeChinese} 60%,
            rgba(238, 108, 69, 0.3) 85%,
            transparent 100%)`,
          boxShadow: `
            0 0 80px ${SUNRISE_COLORS.yellowLight}40,
            0 0 120px ${SUNRISE_COLORS.orangeChinese}20,
            inset 0 0 40px ${SUNRISE_COLORS.yellowVeryLight}60
          `,
          opacity: 0,
          zIndex: 8 // CISCO: Derrière le paysage (z-index 10)
        }}
      />

      {/* ⭐ ÉTOILES QUI DISPARAISSENT (CISCO: plus nombreuses au démarrage) */}
      <div
        ref={starsContainerRef}
        className="absolute inset-0"
        style={{ zIndex: 9999 }} // CISCO: Au-dessus de tout pour visibilité garantie
      >
        {sunriseStars.map((star) => (
          <div
            key={star.id}
            className={`absolute rounded-full ${star.type === 'big' ? 'bg-yellow-100' : 'bg-white'}`}
            style={{
              left: `${star.x}%`,
              top: `${star.y}%`,
              width: `${star.size}px`,
              height: `${star.size}px`,
              opacity: star.opacity,
              // CISCO: CORRECTION - Animation naturelle avec durée et délai variables
              animation: `naturalTwinkle ${star.twinkleDuration}s ease-in-out infinite ${star.twinkleDelay}s`,
              boxShadow: star.type === 'big' ? `0 0 ${star.size * 2}px rgba(255, 255, 255, 0.6)` : 'none'
            }}
          />
        ))}
      </div>

      {/* 💡 ÉCLAIRAGE GLOBAL PROGRESSIF (CISCO: synchronisé avec le soleil) */}
      <div
        ref={globalLightRef}
        className="absolute inset-0"
        style={{
          background: `radial-gradient(ellipse at 75% 60%,
            ${SUNRISE_COLORS.yellowVeryLight}15 0%,
            ${SUNRISE_COLORS.orangeChinese}08 30%,
            ${SUNRISE_COLORS.roseDark}05 50%,
            transparent 75%)`,
          opacity: 0,
          zIndex: 6
        }}
      />

      {/* 🏔️ RÉFÉRENCE PAYSAGE POUR ÉCLAIRAGE (CISCO: s'éclaircit progressivement) */}
      <div
        ref={landscapeRef}
        className="absolute inset-0"
        style={{
          background: 'transparent',
          filter: 'brightness(0.15)', // Démarrage très sombre
          zIndex: 7
        }}
      />

      {/* 🎨 STYLES POUR ANIMATIONS (CISCO: scintillement naturel) */}
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes naturalTwinkle {
            0% {
              opacity: 0.4;
              filter: brightness(0.8);
            }
            25% {
              opacity: 0.7;
              filter: brightness(1.0);
            }
            50% {
              opacity: 1;
              filter: brightness(1.2);
            }
            75% {
              opacity: 0.6;
              filter: brightness(0.9);
            }
            100% {
              opacity: 0.4;
              filter: brightness(0.8);
            }
          }

          /* CISCO: Styles pour debug et info */
          .sunrise-debug {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
          }
        `
      }} />

      {/* 🔧 CISCO: DEBUG INFO (optionnel) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="sunrise-debug">
          <div>🌅 ModeLeverSoleil.tsx</div>
          <div>Intensité: {(isProgressing ? currentIntensity : intensity).toFixed(3)}</div>
          <div>Phase: {currentPhase}</div>
          <div>Progression: {isProgressing ? 'OUI' : 'NON'}</div>
          <div>Étoiles: {sunriseStars.length}</div>
        </div>
      )}
    </div>
  );
};

export default ModeLeverSoleil;
