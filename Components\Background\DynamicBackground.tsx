import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
// 🧹 CISCO: NETTOYAGE - AstronomicalLayer supprimé
import DiurnalLayer from './DiurnalLayer';
// 🌅 CISCO: NOUVEAU MODULE LEVER DE SOLEIL COMPLET
import Mode<PERSON><PERSON><PERSON>oleil from './ModeLeverSoleil';
// 🎬 CISCO: SUPPRESSION useDayCycleOptional - Remplacé par props directes du système Cinema
// 🎨 CISCO: IMPORT DES VRAIES PALETTES - Remplacement des couleurs pastels (SUPPRIMÉ - non utilisé)

// 🔧 CISCO: Système de rotation supprimé - Background fixe pour éviter les changements automatiques

// 🔧 CISCO: Fonction supprimée - Background fixe pour éviter les changements automatiques

// 🌅 CISCO: NETTOYAGE COMPLET - SEULEMENT MODULE LEVER DE SOLEIL
// Type pour le mode de fond - SEUL MODE AUTORISÉ
type BackgroundMode = 'leverSoleil'; // 🌅 CISCO: SEUL MODULE LEVER DE SOLEIL

// 🔧 CISCO: MODES SUPPRIMÉS (simplification) : sunrise, morning, afternoon, dusk

// 🎨 CISCO: VRAIES PALETTES CISCO - Remplacement complet des couleurs pastels
// ✨ NOUVELLES COULEURS: Extraites des vraies captures d'écran de ciels naturels
// Dégradés fluides synchronisés avec le temporisateur de journée

// 🎬 CISCO: PALETTES CINÉMATOGRAPHIQUES SIMPLIFIÉES - 4 MODES ESSENTIELS
// Dégradés cohérents selon les règles d'or CISCO (nuages/paysage/arrière-plan)
const BACKGROUND_MODES = {
  // 🌅 === AUBE === 🌅
  // CISCO: Lumière douce progressive - Orange rosé à l'horizon
  dawn: {
    primary: '#ff8a65',    // Bas : Orange rosé clair (aube arrive à l'horizon)
    secondary: '#5a6b7d',  // Milieu : Gris bleu (transition douce)
    tertiary: '#2d3748'    // Haut : Bleu nuit (continuité nocturne)
  },

  // ☀️ === MIDI === ☀️
  // CISCO: Lumière au maximum - Bleu ciel éclatant uniforme
  midday: {
    primary: '#e3f2fd',    // Bas : Bleu très clair (horizon lumineux)
    secondary: '#90caf9',  // Milieu : Bleu clair éclatant
    tertiary: '#42a5f5'    // Haut : Bleu pur intense (zénith parfait)
  },

  // 🌇 === COUCHER === 🌇
  // CISCO: Lumière s'atténue - Orange/rouge intense à l'horizon
  sunset: {
    primary: '#ff7043',    // Bas : Orange/rouge intense (soleil à l'horizon)
    secondary: '#ab7967',  // Milieu : Brun orangé (transition)
    tertiary: '#5c6bc0'    // Haut : Bleu (transition vers nuit)
  },

  // 🌌 === NUIT === 🌌
  // CISCO: Lumière au minimum - Bleu nuit profond uniforme
  night: {
    primary: '#0d1117',    // Bas : Bleu nuit très sombre (horizon nocturne)
    secondary: '#1c2938',  // Milieu : Bleu nuit intermédiaire
    tertiary: '#2d3748'    // Haut : Bleu nuit plus clair (étoiles visibles)
  }
};

// 🔧 CISCO: TRANSITIONS AVEC VRAIES COULEURS - Ponts naturels entre les modes
// Ces transitions permettent de passer en douceur d'un mode à un autre en utilisant des couleurs intermédiaires.
// Les couleurs sont définies pour trois zones de l'écran :
// - `primary` : correspond à la partie basse de l'écran (proche de l'horizon, le plus clair).
// - `secondary` : correspond à la partie intermédiaire de l'écran (milieu du ciel, intermédiaire).
// - `tertiary` : correspond à la partie haute de l'écran (proche du zénith, le plus sombre).
// Les pourcentages définissent la répartition verticale des couleurs sur l'écran.

// 🎬 CISCO: TRANSITIONS CINÉMATOGRAPHIQUES SIMPLIFIÉES - 3 transitions essentielles
const TRANSITION_MODES = {
  // Transition de l'aube vers midi
  'dawn-midday': {
    primary: '#b8d4f1',     // Bas : Transition orange rosé vers bleu clair
    secondary: '#7ba3d0',   // Milieu : Bleu intermédiaire
    tertiary: '#4a7bc8',    // Haut : Bleu vers zénith
    percentages: [60, 30, 10]
  },
  // Transition de midi vers coucher
  'midday-sunset': {
    primary: '#f5c99b',     // Bas : Bleu clair vers orange chaud
    secondary: '#d4a373',   // Milieu : Transition dorée
    tertiary: '#8b5a3c',    // Haut : Orange vers brun
    percentages: [60, 30, 10]
  },
  // Transition du coucher vers nuit
  'sunset-night': {
    primary: '#6b4e3d',     // Bas : Orange sombre vers bleu nuit
    secondary: '#3d3a4f',   // Milieu : Transition violacée
    tertiary: '#1a1f2e',    // Haut : Bleu nuit profond
    percentages: [60, 30, 10]
  }
};

// Interface pour les props du composant
interface DynamicBackgroundProps {
  children: React.ReactNode;
  skyMode?: string; // 🔧 CISCO: Optionnel si contexte disponible
}



const DynamicBackground: React.FC<DynamicBackgroundProps> = ({ children, skyMode }) => {
  const [isTransitioning, setIsTransitioning] = useState(false);

  // 🎬 CISCO: SYSTÈME CINÉMATOGRAPHIQUE SIMPLIFIÉ - Props directes uniquement
  const defaultMode = 'dawn';
  const validatedSkyMode = skyMode && typeof skyMode === 'string' ? skyMode : defaultMode;
  const currentModeRef = useRef(validatedSkyMode);
  const backgroundRef = useRef<HTMLDivElement>(null);
  const gradientRef = useRef<HTMLDivElement>(null);
  const landscapeRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);
  const zoomTimelineRef = useRef<gsap.core.Timeline | null>(null);
  // 🔧 CISCO: sunriseAnimationRef supprimé - géré dans AstronomicalLayer

  // 🔧 CISCO: Background UNIQUE - Background.png seulement (simplification)
  const selectedBackground = '/Background.png'; // Background unique pour simplifier

  // 🔧 CISCO: Position simplifiée pour Background.png unique
  const getBackgroundPosition = (): string => {
    return 'center bottom -200px'; // 🔧 CISCO: Paysage complètement en bas pour MAXIMUM de ciel dégagé et lune visible rapidement
  };
  
  // 🔧 CISCO: SUPPRESSION COMPLÈTE - Plus de fonction automatique basée sur l'heure
  // const getModeForTime = ... // SUPPRIMÉ - Mode manuel uniquement



  // 🔧 CISCO: ANCIEN SYSTÈME SUPPRIMÉ - DiurnalLayer s'occupe maintenant de tout

  // 🔧 CISCO: Fonction pour les transitions d'étoiles - IMPLÉMENTÉE
  const applyStarsTransition = (mode: BackgroundMode, duration: number) => {
    console.log(`⭐ Transition des étoiles vers ${mode} (${duration}s)`);

    // Déclencher la mise à jour du mode dans AstronomicalLayer via l'état
    // Le composant AstronomicalLayer recevra automatiquement le nouveau skyMode
    // et FixedStars se chargera de la transition des étoiles

    // Pas besoin d'action directe ici car le skyMode est passé en props
    // et les useEffect dans FixedStars gèrent les transitions automatiquement
  };



  // 🎬 CISCO: LOGIQUE CINÉMATOGRAPHIQUE - DIRECTIONS DÉGRADÉS SELON CYCLE SOLAIRE
  // 🌅 PHASES MONTANTES (Soleil monte) : NUIT → AUBE → dégradés `to top`
  // 🌇 PHASES DESCENDANTES (Soleil descend) : MIDI → COUCHER → dégradés `to bottom`

  const getGradientDirection = (mode: BackgroundMode): string => {
    // 🌅 PHASES MONTANTES - Lumière arrive par l'horizon, monte vers le ciel
    if (['night', 'dawn'].includes(mode)) {
      return 'to top';
    }
    // 🌇 PHASES DESCENDANTES - Lumière part du zénith, descend vers l'horizon
    if (['midday', 'sunset'].includes(mode)) {
      return 'to bottom';
    }
    return 'to top'; // Fallback
  };

  // 🌅 CISCO: NETTOYAGE COMPLET - TOUTES LES ANCIENNES FONCTIONS SUPPRIMÉES
  // Seul le module ModeLeverSoleil.tsx gère maintenant le lever de soleil

  // 🎬 CISCO: applyMorningMode SUPPRIMÉ - Simplifié vers 4 modes cinématographiques

  // 🔧 CISCO: FONCTION DE RÉINITIALISATION COMPLÈTE avec toutes les phases
  const resetAllLayers = (targetMode: BackgroundMode) => {
    console.log(`🔄 RÉINITIALISATION COMPLÈTE pour mode: ${targetMode} - Direction: ${getGradientDirection(targetMode)}`);

    // Forcer la mise à jour du mode immédiatement pour que les couches se régénèrent
    currentModeRef.current = targetMode;

    // 🌅 CISCO: NETTOYAGE COMPLET - SEUL MODE leverSoleil
    if (targetMode === 'leverSoleil') {
      // 🌅 CISCO: SEUL MODE LEVER DE SOLEIL - Forcer le démarrage SOMBRE
      console.log('🌅 CISCO: Mode ModeLeverSoleil SEUL activé - FORÇAGE DÉMARRAGE SOMBRE');

      // CISCO: NETTOYAGE - Forcer tout en mode sombre au démarrage
      if (gradientRef.current) {
        gsap.set(gradientRef.current, {
          backgroundImage: 'linear-gradient(to top, #0B1426 0%, #041A40 50%, #0B1426 100%)',
          duration: 0
        });
      }

      if (landscapeRef.current) {
        gsap.set(landscapeRef.current, {
          filter: 'brightness(0.15)', // Très sombre
          duration: 0
        });
      }
    } else {
      // CISCO: NETTOYAGE - Forcer leverSoleil pour tout autre mode
      console.warn(`⚠️ Mode ${targetMode} supprimé, redirection vers leverSoleil`);
      setBackgroundMode('leverSoleil');
    }

    // Les useEffect des couches (DiurnalLayer, AstronomicalLayer) vont se déclencher
    // automatiquement grâce au changement de skyMode en props
  };

  // 🌅 CISCO: NETTOYAGE COMPLET - TOUTES LES ANCIENNES FONCTIONS SUPPRIMÉES
  // Seul le module ModeLeverSoleil.tsx gère maintenant TOUT

  // 🎬 CISCO: applyDuskMode SUPPRIMÉ - Simplifié vers 4 modes cinématographiques

  // 🔧 CISCO: Changement de mode avec CROSS FADE progressif TOUJOURS
  const setBackgroundMode = (mode: BackgroundMode) => {
    // 🚀 CISCO: OPTIMISATION - Éviter les transitions inutiles
    if (currentModeRef.current === mode && !isTransitioning) {
      console.log(`⚡ Mode ${mode} déjà actif, transition ignorée`);
      return;
    }

    // 🔧 CISCO: PROTECTION RENFORCÉE - Arrêter TOUTES les animations en cours
    if (timelineRef.current && timelineRef.current.isActive()) {
      console.log('🛑 Interruption animation en cours pour nouvelle transition');
      timelineRef.current.kill();
      setIsTransitioning(false); // 🔧 CISCO: Forcer la réinitialisation du flag
    }

    // 🔧 CISCO: PROTECTION ANTI-BLOCAGE - Forcer déblocage après 20s max
    if (isTransitioning) {
      console.log('⏳ Transition en cours, vérification anti-blocage...');
      setTimeout(() => {
        if (isTransitioning) {
          console.log('🔓 DÉBLOCAGE FORCÉ - Transition bloquée > 20s');
          setIsTransitioning(false);
          setBackgroundMode(mode); // Relancer la transition
        }
      }, 20000);
      return;
    }

    // Si c'est le même mode, ne rien faire (évite le spam de logs)
    if (mode === currentModeRef.current) {
      console.log('🔄 Mode identique, pas de transition');
      return;
    }

    console.log(`🎨 Changement de mode vers: ${mode} depuis ${currentModeRef.current}`);

    // 🔧 CISCO: RÉINITIALISATION FORCÉE pour modes non adjacents
    const transitionKey = `${currentModeRef.current}-${mode}` as keyof typeof TRANSITION_MODES;
    const isAdjacentTransition = TRANSITION_MODES[transitionKey];

    if (!isAdjacentTransition) {
      console.log(`🔄 TRANSITION NON ADJACENTE détectée: ${currentModeRef.current} → ${mode} - RÉINITIALISATION FORCÉE`);
      resetAllLayers(mode);
    }

    // Transition avec pont si modes adjacents
    if (isAdjacentTransition) {
      console.log(`🌉 Utilisation du pont de transition: ${transitionKey}`);
      // Appliquer d'abord la couleur de transition
      updateBackgroundWithBridge(mode, transitionKey);
    } else {
      // 🔧 AMÉLIORATION: Transition directe mais DOUCE pour modes non adjacents
      console.log(`🎨 Transition directe douce vers: ${mode}`);
      // ✅ CORRECTION: Ne pas changer currentMode immédiatement pour éviter le changement brutal
      updateBackgroundSmoothly(mode); // Cette fonction se chargera de mettre à jour le mode
    }
  };

  // 🔧 NOUVELLE FONCTION: Transition douce avec directions correctes selon logique solaire
  const updateBackgroundSmoothly = (targetMode: BackgroundMode) => {
    if (!gradientRef.current) return;

    const targetColors = getColorsForMode(targetMode);
    const direction = getGradientDirection(targetMode);

    setIsTransitioning(true);

    // 🎨 CISCO: Dégradé final avec direction correcte selon logique solaire
    let finalGradient;
    if (direction === 'to top') {
      // PHASES MONTANTES - Couleurs dans l'ordre normal
      finalGradient = `linear-gradient(${direction}, ${targetColors.primary} 0%, ${targetColors.secondary} 60%, ${targetColors.tertiary} 100%)`;
    } else {
      // PHASES DESCENDANTES - Couleurs inversées pour effet descendant
      finalGradient = `linear-gradient(${direction}, ${targetColors.tertiary} 0%, ${targetColors.secondary} 60%, ${targetColors.primary} 100%)`;
    }

    const targetBrightness = 0.15; // CISCO: Sombre par défaut

    console.log(`🎨 Transition douce vers ${targetMode} - Direction: ${direction}`);

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline({
      onComplete: () => {
        setIsTransitioning(false);
        currentModeRef.current = targetMode;
        console.log(`✨ Transition douce vers ${targetMode} terminée ! Direction: ${direction}`);
      }
    });

    // 🌊 CISCO: SYNCHRONISATION PARFAITE - 25 secondes avec directions correctes
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: finalGradient,
      duration: 25.0, // 🔧 CISCO: Harmonisation temporisateur
      ease: "power1.inOut", // Easing doux pour vraies couleurs
      force3D: true,
      willChange: "background-image"
    });

    // 🎬 CISCO: TRANSITION DE L'ÉCLAIRAGE CINÉMATOGRAPHIQUE - Durée uniforme
    const transitionDuration = 25.0;
    if (landscapeRef.current) {
      timelineRef.current.to(landscapeRef.current, {
        filter: `brightness(${targetBrightness})`,
        duration: transitionDuration,
        ease: "power1.inOut"
      }, 0);
    }

  };

  // 🔧 NOUVELLE FONCTION: Transition avec pont intermédiaire et directions correctes
  const updateBackgroundWithBridge = (targetMode: BackgroundMode, transitionKey: keyof typeof TRANSITION_MODES) => {
    if (!gradientRef.current) return;

    const bridgeColors = TRANSITION_MODES[transitionKey];
    const targetColors = getColorsForMode(targetMode);
    const direction = getGradientDirection(targetMode);

    setIsTransitioning(true);

    // 🎨 CISCO: Dégradé de pont avec direction correcte
    let bridgeGradient;
    if (direction === 'to top') {
      bridgeGradient = `linear-gradient(${direction}, ${bridgeColors.primary} 0%, ${bridgeColors.secondary} 60%, ${bridgeColors.tertiary} 100%)`;
    } else {
      bridgeGradient = `linear-gradient(${direction}, ${bridgeColors.tertiary} 0%, ${bridgeColors.secondary} 60%, ${bridgeColors.primary} 100%)`;
    }

    // 🎨 CISCO: Dégradé final avec direction correcte selon logique solaire
    let finalGradient;
    if (direction === 'to top') {
      finalGradient = `linear-gradient(${direction}, ${targetColors.primary} 0%, ${targetColors.secondary} 60%, ${targetColors.tertiary} 100%)`;
    } else {
      finalGradient = `linear-gradient(${direction}, ${targetColors.tertiary} 0%, ${targetColors.secondary} 60%, ${targetColors.primary} 100%)`;
    }

    const targetBrightness = 0.15; // CISCO: Sombre par défaut

    console.log(`🌉 Transition avec pont vers ${targetMode} - Direction: ${direction}`);

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline({
      onComplete: () => {
        currentModeRef.current = targetMode;
        setIsTransitioning(false);
        console.log(`✨ Transition avec pont vers ${targetMode} terminée ! Direction: ${direction}`);
      }
    });

    // 🌉 CISCO: PHASE 1 - Transition vers le pont avec direction correcte
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: bridgeGradient,
      duration: 12.0,
      ease: "power0.5.inOut",
      force3D: true,
      willChange: "background-image"
    });

    // 🌉 CISCO: PHASE 2 - Transition du pont vers couleurs finales avec direction correcte
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: finalGradient,
      duration: 12.0,
      ease: "power0.5.inOut",
      force3D: true,
      willChange: "background-image"
    }, 12.0);

    // 🎬 CISCO: TRANSITION DE L'ÉCLAIRAGE CINÉMATOGRAPHIQUE - Durée uniforme
    const transitionDuration = 40.0;
    if (landscapeRef.current) {
      timelineRef.current.to(landscapeRef.current, {
        filter: `brightness(${targetBrightness})`,
        duration: transitionDuration,
        ease: "power0.5.inOut"
      }, 0);
    }

    // 🔧 CISCO: SYNCHRONISATION DES ÉTOILES - MÊME TIMING
    timelineRef.current.call(() => {
      applyStarsTransition(targetMode, transitionDuration);
    }, [], 0.1);
  };

  // 🔧 FONCTION SIMPLIFIÉE: Obtenir les couleurs pour un mode donné
  const getColorsForMode = (mode: BackgroundMode) => {
    // 🔧 CISCO: Protection renforcée contre les valeurs undefined/null
    if (!mode || mode === undefined || mode === null) {
      console.warn(`🔧 Mode invalide: ${mode}, utilisation du mode par défaut 'dawn'`);
      return BACKGROUND_MODES['dawn'];
    }

    // 🌅 CISCO: Mode leverSoleil utilise les couleurs dawn par défaut (le module gère ses propres couleurs)
    if (mode === 'leverSoleil') {
      return BACKGROUND_MODES['dawn'];
    }

    const colors = BACKGROUND_MODES[mode as keyof typeof BACKGROUND_MODES];
    // 🔧 CISCO: Protection contre les modes non définis dans BACKGROUND_MODES
    if (!colors) {
      console.warn(`🔧 Mode non trouvé dans BACKGROUND_MODES: ${mode}, utilisation du mode par défaut 'dawn'`);
      return BACKGROUND_MODES['dawn'];
    }
    return colors;
  };

  // 🌅 CISCO: NETTOYAGE - Fonction brightness supprimée
  // Le module ModeLeverSoleil.tsx gère son propre éclairage


  // 🔧 FONCTION PRINCIPALE: Transition progressive fluide entre modes
  const updateDynamicBackground = (mode?: BackgroundMode) => {
    // 🔧 CISCO: PROTECTION RENFORCÉE - Arrêter animations actives avant nouvelle transition
    if (timelineRef.current && timelineRef.current.isActive()) {
      console.log('🛑 Interruption animation active dans updateDynamicBackground');
      timelineRef.current.kill();
    }

    if (isTransitioning) { console.log('⏳ Transition déjà en cours, updateDynamicBackground ignoré'); return; }
    if (!gradientRef.current) return;

    const targetMode = mode || skyMode as BackgroundMode;
    const colors = getColorsForMode(targetMode);
    
    // 🎬 INDICATEUR DE TRANSITION
    setIsTransitioning(true);

    // 🎨 CISCO: Dégradé fluide avec direction correcte selon logique solaire
    const direction = getGradientDirection(targetMode);
    let gradient;
    if (direction === 'to top') {
      // PHASES MONTANTES - Couleurs dans l'ordre normal
      gradient = `linear-gradient(${direction}, ${colors.primary} 0%, ${colors.secondary} 60%, ${colors.tertiary} 100%)`;
    } else {
      // PHASES DESCENDANTES - Couleurs inversées pour effet descendant
      gradient = `linear-gradient(${direction}, ${colors.tertiary} 0%, ${colors.secondary} 60%, ${colors.primary} 100%)`;
    }
    
    const brightness = 0.15; // CISCO: Sombre par défaut

    console.log(`🎨 Transition progressive fluide vers ${targetMode}`);

    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    timelineRef.current = gsap.timeline({
      onComplete: () => {
        setIsTransitioning(false);
        currentModeRef.current = targetMode; // ✅ Mettre à jour le mode courant pour éviter toute re-boucle
        console.log(`✨ Transition vers ${targetMode} terminée !`);
      }
    });

    // 🌅 CISCO: TRANSITION DIRECTE - PLUS DOUCE ET PROGRESSIVE
    timelineRef.current.to(gradientRef.current, {
      backgroundImage: gradient,
      duration: 35.0, // 🔧 CISCO: Plus long pour transition très douce
      ease: "power0.5.inOut", // 🔧 CISCO: Easing très doux
      force3D: true,
      willChange: "background-image"
    });

    // 🎬 CISCO: TRANSITION DE L'ÉCLAIRAGE CINÉMATOGRAPHIQUE - DOUCE ET PROGRESSIVE
    const transitionDuration = 40.0; // 🎬 CISCO: Durée uniforme pour tous les modes
    if (landscapeRef.current) {
      timelineRef.current.to(landscapeRef.current, {
        filter: `brightness(${brightness})`,
        duration: transitionDuration, // 🔧 CISCO: Transitions très douces
        ease: "power0.5.inOut" // 🔧 CISCO: Easing très doux
      }, 0);
    }

  };

  // Animation de zoom du paysage
  const createLandscapeZoomAnimation = () => {
    if (!landscapeRef.current) return;
    if (zoomTimelineRef.current) {
      zoomTimelineRef.current.kill();
    }
    zoomTimelineRef.current = gsap.timeline({ repeat: -1, yoyo: false, force3D: true });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.15, duration: 45, ease: "power2.inOut" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.15, duration: 5, ease: "none" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.0, duration: 35, ease: "power2.out" });
    zoomTimelineRef.current.to(landscapeRef.current, { scale: 1.0, duration: 10, ease: "none" });
  };

  // 🔧 CISCO: Fonctions soleil supprimées - gérées dans AstronomicalLayer

  // 🌅 CISCO: NETTOYAGE - Fonction nuit supprimée
  // Seul leverSoleil est autorisé

  // 🌅 CISCO: NETTOYAGE - Plus d'exposition de fonctions
  // Seul ModeLeverSoleil.tsx gère tout

  // Initialisation une seule fois avec vraies couleurs Cisco
  useEffect(() => {
    if (gradientRef.current) {
      const initialColors = getColorsForMode(validatedSkyMode as BackgroundMode);
      // 🔧 CISCO: Dégradé initial avec vraies couleurs extraites
      const initialGradient = `linear-gradient(to top, ${initialColors.primary} 0%, ${initialColors.secondary} 60%, ${initialColors.tertiary} 100%)`;
      gsap.set(gradientRef.current, {
        backgroundImage: initialGradient
      });
      console.log('🎨 Dégradé initial appliqué avec vraies couleurs Cisco');
    }

    createLandscapeZoomAnimation();
    updateDynamicBackground();

    // 🌅 CISCO: NETTOYAGE - Initialiser sombre pour leverSoleil
    if (landscapeRef.current) {
      gsap.set(landscapeRef.current, {
        filter: 'brightness(0.15)' // Très sombre au démarrage
      });
      console.log('💡 Éclairage paysage initialisé SOMBRE pour leverSoleil');
    }

    return () => {
      if (timelineRef.current) timelineRef.current.kill();
      if (zoomTimelineRef.current) zoomTimelineRef.current.kill();
    };
  }, []);

  // 🎬 CISCO: SYNCHRONISATION CINÉMATOGRAPHIQUE SIMPLIFIÉE - Props directes uniquement
  useEffect(() => {
    if (skyMode && skyMode !== (currentModeRef.current as string)) {
      // Mode cinématographique via props
      console.log(`🎬 Mode cinématographique: ${skyMode}`);
      setBackgroundMode(skyMode as BackgroundMode);
    } else if (!skyMode) {
      // Mode par défaut si pas de props
      console.log('🌅 Initialisation mode par défaut: dawn (aube)');
      setBackgroundMode(defaultMode as BackgroundMode);
    }
  }, [validatedSkyMode, skyMode]);



  // 🔧 CISCO: Exposer la fonction de changement de mode pour le contrôleur
  useEffect(() => {
    (window as any).setBackgroundMode = (mode: string) => {
      console.log(`🎨 Changement de mode via contrôleur: ${mode}`);
      setBackgroundMode(mode as BackgroundMode);
    };

    return () => {
      delete (window as any).setBackgroundMode;
    };
  }, []);

  return (
    <div
      ref={backgroundRef}
      className="absolute inset-0"
      style={{
        zIndex: 0,
        minHeight: '100vh',
        // 🔧 CISCO: CORRECTION SCROLL - Retour à absolute pour permettre les interactions
        // Le dégradé sera fixé différemment
      }}
    >
      {/* Conteneur pour le dégradé - commence plus haut pour l'aube */}
      <div
        ref={gradientRef}
        className="fixed inset-0 pointer-events-none"
        style={{
          zIndex: -1,
          // 🔧 CISCO: CORRECTION SCROLL - Dégradé fixe avec z-index négatif
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover'
          // ✅ CORRECTION: Supprimer le fallbackGradient qui entre en conflit avec GSAP
        }}
      />

      {/* Couches avec nuages réduits - Mode synchronisé avec le contexte */}
      {/* 🧹 CISCO: NETTOYAGE - AstronomicalLayer supprimé */}
      <DiurnalLayer skyMode={validatedSkyMode as BackgroundMode} />

      {/* 🔧 CISCO: SunriseAnimation déplacé dans AstronomicalLayer */}

      {/* Paysage avec éclairage dynamique - Background aléatoire */}
      <div
        ref={landscapeRef}
        className="fixed inset-0 w-full h-full bg-cover bg-center bg-no-repeat pointer-events-none"
        style={{
          backgroundImage: `url(${selectedBackground})`,
          backgroundPosition: getBackgroundPosition(), // Position pour Background.png
          backgroundSize: 'cover', // Taille standard pour tous les backgrounds
          zIndex: 10, // 🔧 CISCO: Paysage en avant-plan (z-index 10)
          transformOrigin: 'center center',
          willChange: 'transform, filter'
        }}
      />

      {/* 🌅 CISCO: MODULE LEVER DE SOLEIL COMPLET */}
      <ModeLeverSoleil
        isActive={skyMode === 'leverSoleil'}
        autoStart={true}
        intensity={0.0}
      />

      {/* Contenu principal */}
      <div className="relative" style={{ zIndex: 15 }}>
        {children}
      </div>

      {/* 🔧 CISCO: Indicateur de transition - Z-index élevé pour éviter conflit avec Header */}
      {isTransitioning && (
        <div className="fixed top-4 right-4 bg-[#0D9488]/90 text-white px-4 py-2 rounded-lg backdrop-blur-sm z-[70] shadow-lg border border-[#A550F5]/30">
          <div className="flex items-center gap-2">
            <div className="animate-pulse">
              ✨
            </div>
            <span className="text-sm font-medium">
              Transition...
            </span>
          </div>
        </div>
      )}

      <style dangerouslySetInnerHTML={{
        __html: `
          body, html {
            background: none !important;
            background-color: transparent !important;
            overflow-x: hidden !important;
            /* 🔧 CISCO: Permettre le scroll vertical mais masquer l'ascenseur */
            overflow-y: auto !important;
          }

          /* 🔧 CISCO: MASQUER L'ASCENSEUR DÉTESTÉ mais garder le scroll fonctionnel */
          ::-webkit-scrollbar {
            width: 0px;
            background: transparent;
          }

          ::-webkit-scrollbar-track {
            background: transparent;
          }

          ::-webkit-scrollbar-thumb {
            background: transparent;
          }

          /* Pour Firefox */
          html {
            scrollbar-width: none;
          }

          /* Pour Internet Explorer et Edge */
          body {
            -ms-overflow-style: none;
          }
        `
      }} />
    </div>
  );
};

export default DynamicBackground;
